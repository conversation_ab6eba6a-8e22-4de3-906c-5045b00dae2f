import React, { useState } from "react";
import SellerLayout from "../../components/seller/SellerLayout";
import { FiUpload } from "react-icons/fi";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import "../../styles/AddStrategy.css";

const AddStrategy = () => {
  // State for form data including rich text content
  const [formData, setFormData] = useState({
    title: "Advanced Basketball Shooting Techniques",
    category: "basketball",
    price: "299",
    description: "Enter a detailed description of your strategy here. Explain the key concepts, methodologies, and expected outcomes that buyers can expect from this strategy.",
    aboutCoach: "Share your background, experience, and expertise. Tell potential buyers about your coaching philosophy, achievements, and what makes your approach unique.",
    strategicContent: "Outline what strategic content is included in this package. Detail the specific materials, resources, insights, and actionable steps provided to buyers."
  });

  // React Quill configuration
  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'color': [] }, { 'background': [] }],
      ['link'],
      ['clean']
    ],
  };

  const quillFormats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'color', 'background', 'link'
  ];

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle Quill editor changes
  const handleQuillChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <SellerLayout>
      <div className="AddStrategy">
        {/* Main Form */}
        <div className="AddStrategy__form">
          {/* Title Input */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Strategy Title</label>
            <input
              type="text"
              name="title"
              className="AddStrategy__input"
              placeholder="Add title for New Strategy"
              value={formData.title}
              onChange={handleInputChange}
            />
          </div>

          {/* Video Category Dropdown */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Video Category</label>
            <select
              name="category"
              className="AddStrategy__select"
              value={formData.category}
              onChange={handleInputChange}
            >
              <option value="">Select Category</option>
              <option value="basketball">Basketball</option>
              <option value="football">Football</option>
              <option value="soccer">Soccer</option>
              <option value="baseball">Baseball</option>
              <option value="tennis">Tennis</option>
              <option value="golf">Golf</option>
              <option value="other">Other</option>
            </select>
          </div>

          {/* Description Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Description for Strategy</label>
            <div className="AddStrategy__quill-wrapper">
              <ReactQuill
                theme="snow"
                value={formData.description}
                onChange={(value) => handleQuillChange('description', value)}
                modules={quillModules}
                formats={quillFormats}
                placeholder="Enter strategy description..."
                className="AddStrategy__quill"
              />
            </div>
          </div>

          {/* About the Coach Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">About The Coach</label>
            <div className="AddStrategy__quill-wrapper">
              <ReactQuill
                theme="snow"
                value={formData.aboutCoach}
                onChange={(value) => handleQuillChange('aboutCoach', value)}
                modules={quillModules}
                formats={quillFormats}
                placeholder="Tell us about yourself..."
                className="AddStrategy__quill"
              />
            </div>
          </div>

          {/* Includes Strategic Content Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Includes Strategic Content</label>
            <div className="AddStrategy__quill-wrapper">
              <ReactQuill
                theme="snow"
                value={formData.strategicContent}
                onChange={(value) => handleQuillChange('strategicContent', value)}
                modules={quillModules}
                formats={quillFormats}
                placeholder="Describe what's included in this strategy..."
                className="AddStrategy__quill"
              />
            </div>
          </div>

          {/* Upload Section */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Upload Strategy Video</label>
            <div className="AddStrategy__upload">
              <div className="AddStrategy__upload-content">
                <FiUpload className="AddStrategy__upload-icon" />
                <p className="AddStrategy__upload-text">
                  Drag & drop your video file here or click to browse
                </p>
                <p className="AddStrategy__upload-subtext">
                  Supported formats: MP4, MOV, AVI (Max size: 500MB)
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="AddStrategy__actions">
            <button className="btn btn-primary AddStrategy__submit-btn">
              Add New Strategy
            </button>
            <button className="btn btn-outline AddStrategy__reset-btn">
              Reset Form
            </button>
          </div>
        </div>
      </div>
    </SellerLayout>
  );
};

export default AddStrategy;
