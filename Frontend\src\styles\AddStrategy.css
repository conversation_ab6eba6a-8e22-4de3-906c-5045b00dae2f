/* AddStrategy Component Styles */
.AddStrategy {
  width: 100%;
  max-width: 100%;
}



/* Form Section */
.AddStrategy__form {
  display: flex;
  flex-direction: column;
  gap: var(--heading5);
}

.AddStrategy__field {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.AddStrategy__label {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: var(--smallfont);
}

/* Input Styles */
.AddStrategy__input,
.AddStrategy__select,
.AddStrategy__textarea {
  padding: var(--smallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  outline: none;
  font-family: inherit;
}

.AddStrategy__input:focus,
.AddStrategy__select:focus,
.AddStrategy__textarea:focus {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.AddStrategy__input::placeholder,
.AddStrategy__textarea::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.AddStrategy__textarea {
  min-height: 120px;
  resize: vertical;
  line-height: 1.5;
}

/* React Quill Editor Styles */
.AddStrategy__quill-wrapper {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.AddStrategy__quill-wrapper:focus-within {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.AddStrategy__quill .ql-toolbar {
  border: none;
  border-bottom: 1px solid var(--light-gray);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  background-color: var(--bg-gray);
  padding: var(--extrasmallfont) var(--smallfont);
}

.AddStrategy__quill .ql-container {
  border: none;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  font-family: inherit;
  font-size: var(--basefont);
  min-height: 120px;
}

.AddStrategy__quill .ql-editor {
  padding: var(--smallfont) var(--basefont);
  color: var(--text-color);
  line-height: 1.5;
  min-height: 120px;
}

.AddStrategy__quill .ql-editor.ql-blank::before {
  color: var(--dark-gray);
  opacity: 0.7;
  font-style: normal;
}

.AddStrategy__quill .ql-toolbar .ql-stroke {
  stroke: var(--secondary-color);
}

.AddStrategy__quill .ql-toolbar .ql-fill {
  fill: var(--secondary-color);
}

.AddStrategy__quill .ql-toolbar button:hover .ql-stroke {
  stroke: var(--btn-color);
}

.AddStrategy__quill .ql-toolbar button:hover .ql-fill {
  fill: var(--btn-color);
}

.AddStrategy__quill .ql-toolbar button.ql-active .ql-stroke {
  stroke: var(--btn-color);
}

.AddStrategy__quill .ql-toolbar button.ql-active .ql-fill {
  fill: var(--btn-color);
}

.AddStrategy__select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right var(--smallfont) center;
  background-size: 16px;
  padding-right: var(--heading4);
}



/* Upload Section */
.AddStrategy__upload {
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--heading4);
  text-align: center;
  background-color: var(--bg-gray);
  transition: all 0.3s ease;
  cursor: pointer;
}

.AddStrategy__upload:hover {
  border-color: var(--btn-color);
  background-color: rgba(238, 52, 37, 0.05);
}

.AddStrategy__upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--smallfont);
}

.AddStrategy__upload-icon {
  font-size: var(--heading3);
  color: var(--dark-gray);
  margin-bottom: var(--smallfont);
}

.AddStrategy__upload-text {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
  margin: 0;
}

.AddStrategy__upload-subtext {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin: 0;
}

/* Action Buttons */
.AddStrategy__actions {
  display: flex;
  gap: var(--basefont);
  justify-content: flex-start;
  margin-top: var(--heading5);
  padding-top: var(--heading5);
  border-top: 1px solid var(--light-gray);
}

.AddStrategy__submit-btn,
.AddStrategy__reset-btn {
  padding: var(--smallfont) var(--heading5);
  font-size: var(--basefont);
  font-weight: 600;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
  .AddStrategy__form {
    gap: var(--basefont);
  }

  .AddStrategy__actions {
    flex-direction: column;
    gap: var(--smallfont);
  }

  .AddStrategy__submit-btn,
  .AddStrategy__reset-btn {
    width: 100%;
    justify-content: center;
  }



  .AddStrategy__upload {
    padding: var(--basefont);
  }

  .AddStrategy__upload-icon {
    font-size: var(--heading4);
  }
}

@media (max-width: 480px) {
  .AddStrategy__label {
    font-size: var(--smallfont);
  }

  .AddStrategy__input,
  .AddStrategy__select,
  .AddStrategy__textarea {
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--smallfont);
  }

  /* React Quill responsive styles */
  .AddStrategy__quill .ql-toolbar {
    padding: var(--extrasmallfont);
  }

  .AddStrategy__quill .ql-editor {
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--smallfont);
    min-height: 100px;
  }

  .AddStrategy__quill .ql-container {
    min-height: 100px;
  }

  .AddStrategy__upload {
    padding: var(--smallfont);
  }

  .AddStrategy__upload-text {
    font-size: var(--smallfont);
  }

  .AddStrategy__upload-subtext {
    font-size: var(--extrasmallfont);
  }
}
